# Development Environment Variables for SAMe Project

# Database
POSTGRES_PASSWORD=dev_password
POSTGRES_USER=same_user
POSTGRES_DB=same_dev_db

# Redis
REDIS_PASSWORD=dev_redis_password

# Application
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=same_dev_secret_key_2024
API_RELOAD=true
LOG_LEVEL=DEBUG

# Frontend
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_MAX_FILE_SIZE=52428800
REACT_APP_ENVIRONMENT=development

# Development specific
ENABLE_HOT_RELOAD=true
ENABLE_PROFILING=true
CHOKIDAR_USEPOLLING=true

# Jupyter
JUPYTER_TOKEN=same-dev-token
JUPYTER_ENABLE_LAB=yes

# Email testing
MAILHOG_SMTP_HOST=same-mailhog
MAILHOG_SMTP_PORT=1025
