[project]
name = "same2-0"
version = "0.1.0"
description = ""
authors = [
    {name = "igornet0"}
]
readme = "README.md"
requires-python = ">=3.10 <3.14"
dependencies = [
    "pandas (>=2.3.1,<3.0.0)",
    "pydantic-settings (>=2.10.1,<3.0.0)",
    "aiofiles (>=24.1.0,<25.0.0)",
    "spacy (>=3.8.7,<4.0.0)",
    "openpyxl (>=3.1.5,<4.0.0)",
    "scikit-learn (>=1.7.1,<2.0.0)",
    "rapidfuzz (>=3.13.0,<4.0.0)",
    "sentence-transformers (>=5.0.0,<6.0.0)",
    "faiss-cpu (>=1.11.0.post1,<2.0.0)",
    "psutil (>=7.0.0,<8.0.0)",
    "fastapi (>=0.116.1,<0.117.0)",
    "passlib (>=1.7.4,<2.0.0)",
    "sqlalchemy[asyncio] (>=2.0.42,<3.0.0)",
    "asyncpg (>=0.29.0,<0.30.0)",
    "redis (>=5.0.0,<6.0.0)",
    "aio-pika (>=9.5.5,<10.0.0)",
    "matplotlib (>=3.10.5,<4.0.0)",
    "seaborn (>=0.13.2,<0.14.0)",
    "seqeval (>=1.2.2,<2.0.0)",
    "sklearn-crfsuite (>=0.5.0,<0.6.0)",
    "datasets (>=4.0.0,<5.0.0)",
    "uvicorn[standard] (>=0.35.0,<0.36.0)",
    "python-jose[cryptography] (>=3.3.0,<4.0.0)",
    "python-multipart (>=0.0.6,<0.1.0)",
    "alembic (>=1.13.0,<2.0.0)"
]

[tool.poetry]
packages = [{include = "src/"}]


[tool.poetry.group.dev.dependencies]
ipykernel = "^6.30.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
