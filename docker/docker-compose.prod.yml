version: '3.8'

services:
  # Основное приложение SAMe
  same-app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: production
    container_name: same-app
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://same_user:${POSTGRES_PASSWORD}@same-db:5432/same_db
      - REDIS_URL=redis://same-redis:6379/0
      - ELASTICSEARCH_URL=http://same-elasticsearch:9200
    volumes:
      - same-data:/app/data
      - same-models:/app/models
      - same-logs:/app/logs
    depends_on:
      - same-db
      - same-redis
      - same-elasticsearch
    networks:
      - same-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/search/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # База данных PostgreSQL
  same-db:
    image: postgres:15-alpine
    container_name: same-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=same_db
      - POSTGRES_USER=same_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - same-postgres-data:/var/lib/postgresql/data
      - ../scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - same-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U same_user -d same_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis для кэширования
  same-redis:
    image: redis:7-alpine
    container_name: same-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - same-redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - same-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Elasticsearch для полнотекстового поиска
  same-elasticsearch:
    image: elasticsearch:8.11.0
    container_name: same-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - same-elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - same-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React приложение
  same-frontend:
    build:
      context: ../frontend/same-frontend
      dockerfile: Dockerfile
      target: production
    container_name: same-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_BASE_URL=http://same-app:8000
      - REACT_APP_MAX_FILE_SIZE=52428800
      - REACT_APP_ENVIRONMENT=production
    depends_on:
      - same-app
    networks:
      - same-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx как reverse proxy
  same-nginx:
    image: nginx:alpine
    container_name: same-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx/nginx.conf:/etc/nginx/nginx.conf
      - ../nginx/ssl:/etc/nginx/ssl
      - same-logs:/var/log/nginx
    depends_on:
      - same-app
      - same-frontend
    networks:
      - same-network

  # Мониторинг с Prometheus
  same-prometheus:
    image: prom/prometheus:latest
    container_name: same-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ../monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - same-prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - same-network

  # Grafana для визуализации метрик
  same-grafana:
    image: grafana/grafana:latest
    container_name: same-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - same-grafana-data:/var/lib/grafana
      - ../monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ../monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - same-prometheus
    networks:
      - same-network

  # Filebeat для сбора логов
  same-filebeat:
    image: elastic/filebeat:8.11.0
    container_name: same-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ../monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - same-logs:/var/log/same:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - same-elasticsearch
    networks:
      - same-network

  # MinIO для хранения файлов
  same-minio:
    image: minio/minio:latest
    container_name: same-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    volumes:
      - same-minio-data:/data
    command: server /data --console-address ":9001"
    networks:
      - same-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Backup сервис
  same-backup:
    image: postgres:15-alpine
    container_name: same-backup
    restart: "no"
    environment:
      - POSTGRES_DB=same_db
      - POSTGRES_USER=same_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - same-backups:/backups
      - ../scripts/backup.sh:/backup.sh
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"
    depends_on:
      - same-db
    networks:
      - same-network

volumes:
  same-data:
    driver: local
  same-models:
    driver: local
  same-logs:
    driver: local
  same-postgres-data:
    driver: local
  same-redis-data:
    driver: local
  same-elasticsearch-data:
    driver: local
  same-prometheus-data:
    driver: local
  same-grafana-data:
    driver: local
  same-minio-data:
    driver: local
  same-backups:
    driver: local

networks:
  same-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
